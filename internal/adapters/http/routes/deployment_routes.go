package routes

import (
	"ops-api/internal/adapters/http/handlers"

	"github.com/gofiber/fiber/v2"
)

func SetupDeploymentRoutes(api fiber.Router, deploymentHandler *handlers.DeploymentHandler) {
	deployments := api.Group("/deployments")

	// Deployment routes
	deployments.Post("/", deploymentHandler.CreateDeployment)
	deployments.Get("/", deploymentHandler.GetDeployments)
	deployments.Get("/:id", deploymentHandler.GetDeploymentByID)
	deployments.Put("/:id", deploymentHandler.UpdateDeployment)
	deployments.Delete("/:id", deploymentHandler.DeleteDeployment)
}
