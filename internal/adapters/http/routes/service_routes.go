package routes

import (
	"ops-api/internal/adapters/http/handlers"

	"github.com/gofiber/fiber/v2"
)

func RegisterServiceRoutes(router fiber.Router, serviceHandler *handlers.ServiceHandler) {
	services := router.Group("/services")

	services.Post("/", serviceHandler.CreateService)
	services.Get("/", serviceHandler.GetServices)
	services.Get("/:id", serviceHandler.GetServiceByID)
	services.Put("/:id", serviceHandler.UpdateService)
	services.Delete("/:id", serviceHandler.DeleteService)
}
