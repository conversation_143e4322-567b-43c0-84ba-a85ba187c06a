package routes

import (
	"ops-api/internal/adapters/http/handlers"

	"github.com/gofiber/fiber/v2"
)

func RegisterIngressRoutes(router fiber.Router, ingressHandler *handlers.IngressHandler) {
	ingresses := router.Group("/ingresses")

	ingresses.Post("/", ingressHandler.CreateIngress)
	ingresses.Get("/", ingressHandler.GetIngresses)
	ingresses.Get("/:id", ingressHandler.GetIngressByID)
	ingresses.Put("/:id", ingressHandler.UpdateIngress)
	ingresses.Delete("/:id", ingressHandler.DeleteIngress)
}
