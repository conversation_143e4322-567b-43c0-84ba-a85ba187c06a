package handlers

import (
	"strconv"
	"strings"

	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type ServiceHandler struct {
	serviceService ports.ServiceService
}

func NewServiceHandler(serviceService ports.ServiceService) *ServiceHandler {
	return &ServiceHandler{
		serviceService: serviceService,
	}
}

func (h *ServiceHandler) CreateService(c *fiber.Ctx) error {
	var req dto.CreateServiceRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	service, err := h.serviceService.Create(req.Name, req.Port, req.TargetPort, req.Type, req.ClusterIP, req.ExternalIP, req.NamespaceID, req.DeploymentID)
	if err != nil {
		if strings.Contains(err.<PERSON><PERSON><PERSON>(), "access denied") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusCreated, "Service created successfully", dto.ToServiceDetailDTO(service))
}

func (h *ServiceHandler) GetServices(c *fiber.Ctx) error {
	filter := &ports.ServiceFilter{}

	if name := c.Query("name"); name != "" {
		filter.Name = &name
	}

	if namespaceIDStr := c.Query("namespace_id"); namespaceIDStr != "" {
		namespaceID, err := strconv.ParseUint(namespaceIDStr, 10, 64)
		if err != nil {
			return response.Error(c, fiber.StatusBadRequest, "Invalid namespace_id parameter")
		}
		filter.NamespaceID = &namespaceID
	}

	services, err := h.serviceService.GetAll(filter)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	var serviceDTOs []*dto.ServiceListItemResponse
	for _, service := range services {
		serviceDTOs = append(serviceDTOs, dto.ToServiceListItemDTO(service))
	}

	return response.Success(c, fiber.StatusOK, "Services retrieved successfully", serviceDTOs)
}

func (h *ServiceHandler) GetServiceByID(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	service, err := h.serviceService.GetByID(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Service retrieved successfully", dto.ToServiceDetailDTO(service))
}

func (h *ServiceHandler) UpdateService(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	var req dto.UpdateServiceRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	service, err := h.serviceService.Update(id, req.Name, req.Port, req.TargetPort, req.Type, req.ClusterIP, req.ExternalIP, req.NamespaceID, req.DeploymentID, req.StatusID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		if strings.Contains(err.Error(), "access denied") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Service updated successfully", dto.ToServiceDetailDTO(service))
}

func (h *ServiceHandler) DeleteService(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	err = h.serviceService.Delete(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		if strings.Contains(err.Error(), "access denied") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Service deleted successfully", nil)
}
