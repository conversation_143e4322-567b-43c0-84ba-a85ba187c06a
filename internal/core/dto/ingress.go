package dto

import (
	"ops-api/internal/core/domain"
	"time"
)

type CreateIngressRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=50"`
	Class       string `json:"class" validate:"required,oneof=nginx traefik"` // e.g., nginx, traefik
	NamespaceID uint64 `json:"namespace_id" validate:"required"`
}

type UpdateIngressRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=50"`
	Class       string `json:"class" validate:"required,oneof=nginx traefik"` // e.g., nginx, traefik
	NamespaceID uint64 `json:"namespace_id" validate:"required"`
	StatusID    uint64 `json:"status_id" validate:"required"` // The status of the ingress
}

type IngressListItemResponse struct {
	ID        uint64                        `json:"id"`
	CreatedAt time.Time                     `json:"created_at"`
	UpdatedAt time.Time                     `json:"updated_at"`
	Name      string                        `json:"name"`
	Class     string                        `json:"class"`
	Namespace *NamespaceRelationResponse    `json:"namespace,omitempty"`
	Status    *ServerStatusRelationResponse `json:"status,omitempty"`
}

type IngressDetailResponse struct {
	ID           uint64                         `json:"id"`
	CreatedAt    time.Time                      `json:"created_at"`
	UpdatedAt    time.Time                      `json:"updated_at"`
	Name         string                         `json:"name"`
	Class        string                         `json:"class"`
	Namespace    *NamespaceRelationResponse     `json:"namespace,omitempty"`
	Status       *ServerStatusRelationResponse  `json:"status,omitempty"`
	IngressSpecs []*IngressSpecRelationResponse `json:"ingress_specs,omitempty"`
}

type IngressRelationResponse struct {
	ID        uint64                        `json:"id"`
	Name      string                        `json:"name"`
	Class     string                        `json:"class"`
	Namespace *NamespaceRelationResponse    `json:"namespace,omitempty"`
	Status    *ServerStatusRelationResponse `json:"status,omitempty"`
}

type IngressWithIngressSpecsResponse struct {
	ID           uint64                        `json:"id"`
	Name         string                        `json:"name"`
	Class        string                        `json:"class"`
	Namespace    *NamespaceRelationResponse    `json:"namespace,omitempty"`
	Status       *ServerStatusRelationResponse `json:"status,omitempty"`
	IngressSpecs []IngressSpecRelationResponse `json:"ingress_specs,omitempty"`
}

// Convert response

func ToIngressDetailDTO(i *domain.Ingress) *IngressDetailResponse {
	resp := &IngressDetailResponse{
		ID:        i.ID,
		CreatedAt: i.CreatedAt,
		UpdatedAt: i.UpdatedAt,
		Name:      i.Name,
		Class:     i.Class,
		Status:    ToServerStatusRelationDTO(i.Status),
	}

	if i.Namespace != nil {
		resp.Namespace = ToNamespaceRelationDTO(i.Namespace)
	}

	if i.IngressSpecs != nil && len(i.IngressSpecs) > 0 {
		for _, spec := range i.IngressSpecs {
			resp.IngressSpecs = append(resp.IngressSpecs, ToIngressSpecRelationDTO(spec))
		}
	}

	return resp
}

func ToIngressListItemDTO(i *domain.Ingress) *IngressListItemResponse {
	resp := &IngressListItemResponse{
		ID:        i.ID,
		CreatedAt: i.CreatedAt,
		UpdatedAt: i.UpdatedAt,
		Name:      i.Name,
		Class:     i.Class,
		Status:    ToServerStatusRelationDTO(i.Status),
	}

	if i.Namespace != nil {
		resp.Namespace = ToNamespaceRelationDTO(i.Namespace)
	}

	return resp
}

func ToIngressRelationDTO(i *domain.Ingress) *IngressRelationResponse {
	return &IngressRelationResponse{
		ID:   i.ID,
		Name: i.Name,
	}
}
