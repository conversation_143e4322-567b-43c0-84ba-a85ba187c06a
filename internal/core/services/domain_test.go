package services

import (
	"errors"
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"
	"testing"
	"time"
)

// Mock repositories and services for testing
type mockDomainRepository struct {
	domains []domain.Domain
	nextID  uint64
}

func (m *mockDomainRepository) Insert(d *domain.Domain) error {
	m.nextID++
	d.ID = m.nextID
	m.domains = append(m.domains, *d)
	return nil
}

func (m *mockDomainRepository) FindAll(filter *ports.DomainFilter) ([]*domain.Domain, error) {
	var result []*domain.Domain
	for _, d := range m.domains {
		domainCopy := d
		if filter != nil && filter.NamespaceID != nil && *filter.NamespaceID != d.NamespaceID {
			continue
		}
		result = append(result, &domainCopy)
	}
	return result, nil
}

func (m *mockDomainRepository) FindByID(id uint64) (*domain.Domain, error) {
	for _, d := range m.domains {
		if d.ID == id {
			domainCopy := d
			return &domainCopy, nil
		}
	}
	return nil, errors.New("domain not found")
}

func (m *mockDomainRepository) Update(d *domain.Domain) error {
	for i, existing := range m.domains {
		if existing.ID == d.ID {
			m.domains[i] = *d
			return nil
		}
	}
	return errors.New("domain not found")
}

func (m *mockDomainRepository) Delete(id uint64) error {
	for i, d := range m.domains {
		if d.ID == id {
			m.domains = append(m.domains[:i], m.domains[i+1:]...)
			return nil
		}
	}
	return errors.New("domain not found")
}

type mockNamespaceRepository struct {
	namespaces []domain.Namespace
}

func (m *mockNamespaceRepository) FindByID(id uint64) (*domain.Namespace, error) {
	for _, n := range m.namespaces {
		if n.ID == id {
			namespaceCopy := n
			return &namespaceCopy, nil
		}
	}
	return nil, errors.New("namespace not found")
}

func (m *mockNamespaceRepository) FindAll(filter *ports.NamespaceFilter) ([]*domain.Namespace, error) {
	var result []*domain.Namespace
	for _, n := range m.namespaces {
		namespaceCopy := n
		result = append(result, &namespaceCopy)
	}
	return result, nil
}

func (m *mockNamespaceRepository) Insert(n *domain.Namespace) error {
	m.namespaces = append(m.namespaces, *n)
	return nil
}

func (m *mockNamespaceRepository) Update(n *domain.Namespace) error {
	return nil
}

func (m *mockNamespaceRepository) Delete(id uint64) error {
	return nil
}

type mockOrderDomainService struct {
	orderDomains         []domain.OrderDomain
	getByNamespaceResult []*domain.OrderDomain
	updateCalls          []struct {
		id          uint64
		isAvailable bool
	}
}

func (m *mockOrderDomainService) Create(name string, orderID uint64) (*domain.OrderDomain, error) {
	return nil, nil
}

func (m *mockOrderDomainService) GetAll(filter *ports.OrderDomainFilter) ([]*domain.OrderDomain, error) {
	return nil, nil
}

func (m *mockOrderDomainService) GetByID(id uint64) (*domain.OrderDomain, error) {
	return nil, nil
}

func (m *mockOrderDomainService) GetByNamespaceDomain(namespaceID uint64, name string) ([]*domain.OrderDomain, error) {
	return m.getByNamespaceResult, nil
}

func (m *mockOrderDomainService) UpdateAvailability(id uint64, isAvailable bool) (*domain.OrderDomain, error) {
	m.updateCalls = append(m.updateCalls, struct {
		id          uint64
		isAvailable bool
	}{id: id, isAvailable: isAvailable})
	
	// Find and update the order domain
	for i, od := range m.orderDomains {
		if od.ID == id {
			m.orderDomains[i].IsAvailable = isAvailable
			return &m.orderDomains[i], nil
		}
	}
	return nil, errors.New("order domain not found")
}

func (m *mockOrderDomainService) Delete(id uint64) error {
	return nil
}

// Mock services that are not used in this test
type mockIngressSpecService struct{}

func (m *mockIngressSpecService) UpdateHostsByNamespace(namespaceID uint64, domainName string) ([]*domain.IngressSpec, error) {
	return nil, nil
}

type mockDnsService struct{}
type mockOperationService struct{}

func TestDomainService_Create_AutomatedWorkflow(t *testing.T) {
	// Setup mocks
	domainRepo := &mockDomainRepository{nextID: 0}
	namespaceRepo := &mockNamespaceRepository{
		namespaces: []domain.Namespace{
			{BaseModel: domain.BaseModel{ID: 1}, Name: "test-namespace"},
		},
	}
	
	// Create mock order domains that should be updated
	orderDomainService := &mockOrderDomainService{
		orderDomains: []domain.OrderDomain{
			{BaseModel: domain.BaseModel{ID: 1}, Name: "example.com", IsAvailable: false},
			{BaseModel: domain.BaseModel{ID: 2}, Name: "example.com", IsAvailable: false},
		},
		getByNamespaceResult: []*domain.OrderDomain{
			{BaseModel: domain.BaseModel{ID: 1}, Name: "example.com", IsAvailable: false},
			{BaseModel: domain.BaseModel{ID: 2}, Name: "example.com", IsAvailable: false},
		},
	}

	// Create domain service
	domainService := NewDomainService(
		domainRepo,
		namespaceRepo,
		&mockIngressSpecService{},
		&mockDnsService{},
		&mockOperationService{},
		orderDomainService,
	)

	// Test domain creation
	createdDomain, err := domainService.Create(
		"example.com",
		true,
		true,
		"zone123",
		"account123",
		"Test Account",
		1, // namespaceID
	)

	// Verify domain creation was successful
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if createdDomain == nil {
		t.Fatal("Expected domain to be created, got nil")
	}

	if createdDomain.Name != "example.com" {
		t.Errorf("Expected domain name 'example.com', got %s", createdDomain.Name)
	}

	if createdDomain.NamespaceID != 1 {
		t.Errorf("Expected namespace ID 1, got %d", createdDomain.NamespaceID)
	}

	// Wait a bit for the goroutine to complete
	time.Sleep(100 * time.Millisecond)

	// Verify that UpdateAvailability was called for each related order domain
	expectedCalls := 2
	if len(orderDomainService.updateCalls) != expectedCalls {
		t.Errorf("Expected %d UpdateAvailability calls, got %d", expectedCalls, len(orderDomainService.updateCalls))
	}

	// Verify that all calls were made with isAvailable = true
	for i, call := range orderDomainService.updateCalls {
		if !call.isAvailable {
			t.Errorf("Expected UpdateAvailability call %d to have isAvailable=true, got false", i)
		}
		
		expectedID := uint64(i + 1)
		if call.id != expectedID {
			t.Errorf("Expected UpdateAvailability call %d to have id=%d, got %d", i, expectedID, call.id)
		}
	}

	// Verify that the order domains were actually updated
	for _, od := range orderDomainService.orderDomains {
		if !od.IsAvailable {
			t.Errorf("Expected order domain %d to have IsAvailable=true after update", od.ID)
		}
	}
}

func TestDomainService_Create_NoRelatedOrderDomains(t *testing.T) {
	// Setup mocks with no related order domains
	domainRepo := &mockDomainRepository{nextID: 0}
	namespaceRepo := &mockNamespaceRepository{
		namespaces: []domain.Namespace{
			{BaseModel: domain.BaseModel{ID: 1}, Name: "test-namespace"},
		},
	}
	
	orderDomainService := &mockOrderDomainService{
		getByNamespaceResult: []*domain.OrderDomain{}, // No related order domains
	}

	domainService := NewDomainService(
		domainRepo,
		namespaceRepo,
		&mockIngressSpecService{},
		&mockDnsService{},
		&mockOperationService{},
		orderDomainService,
	)

	// Test domain creation
	createdDomain, err := domainService.Create(
		"example.com",
		true,
		true,
		"zone123",
		"account123",
		"Test Account",
		1,
	)

	// Verify domain creation was successful
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if createdDomain == nil {
		t.Fatal("Expected domain to be created, got nil")
	}

	// Wait a bit for the goroutine to complete
	time.Sleep(100 * time.Millisecond)

	// Verify that no UpdateAvailability calls were made
	if len(orderDomainService.updateCalls) != 0 {
		t.Errorf("Expected 0 UpdateAvailability calls, got %d", len(orderDomainService.updateCalls))
	}
}
