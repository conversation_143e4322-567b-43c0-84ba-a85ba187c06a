package services

import (
	"errors"
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"
)

type EnvironmentService struct {
	environmentRepo ports.EnvironmentRepository
	deploymentRepo  ports.DeploymentRepository
}

func NewEnvironmentService(environmentRepo ports.EnvironmentRepository, deploymentRepo ports.DeploymentRepository) ports.EnvironmentService {
	return &EnvironmentService{
		environmentRepo: environmentRepo,
		deploymentRepo:  deploymentRepo,
	}
}

func (s *EnvironmentService) Create(name, value string, deploymentID uint64) (*domain.Environment, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}
	if value == "" {
		return nil, errors.New("value is required")
	}
	if deploymentID == 0 {
		return nil, errors.New("deployment ID is required")
	}

	_, err := s.deploymentRepo.FindByID(deploymentID)
	if err != nil {
		return nil, errors.New("deployment not found")
	}

	environment := &domain.Environment{
		Name:         name,
		Value:        value,
		DeploymentID: deploymentID,
	}

	if err := s.environmentRepo.Insert(environment); err != nil {
		return nil, err
	}

	return s.environmentRepo.FindByID(environment.ID)
}

func (s *EnvironmentService) GetAll(filter *ports.EnvironmentFilter) ([]*domain.Environment, error) {
	return s.environmentRepo.FindAll(filter)
}

func (s *EnvironmentService) GetByID(id uint64) (*domain.Environment, error) {
	if id == 0 {
		return nil, errors.New("id is required")
	}

	environment, err := s.environmentRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("environment not found")
	}

	return environment, nil
}

func (s *EnvironmentService) Update(id uint64, name, value string, deploymentID uint64) (*domain.Environment, error) {
	if id == 0 {
		return nil, errors.New("id is required")
	}
	if name == "" {
		return nil, errors.New("name is required")
	}
	if value == "" {
		return nil, errors.New("value is required")
	}
	if deploymentID == 0 {
		return nil, errors.New("deployment ID is required")
	}

	environment, err := s.environmentRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("environment not found")
	}

	_, err = s.deploymentRepo.FindByID(deploymentID)
	if err != nil {
		return nil, errors.New("deployment not found")
	}

	environment.Name = name
	environment.Value = value
	environment.DeploymentID = deploymentID

	if err := s.environmentRepo.Update(environment); err != nil {
		return nil, err
	}

	return s.environmentRepo.FindByID(id)
}

func (s *EnvironmentService) Delete(id uint64) error {
	if id == 0 {
		return errors.New("id is required")
	}

	_, err := s.environmentRepo.FindByID(id)
	if err != nil {
		return errors.New("environment not found")
	}

	return s.environmentRepo.Delete(id)
}
