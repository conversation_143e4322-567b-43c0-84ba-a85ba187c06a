package services

import (
	"errors"
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"
)

type OrderDomainService struct {
	orderDomainRepo     ports.OrderDomainRepository
	orderRepo           ports.OrderRepository
	orderNamespaceRepo  ports.OrderNamespaceRepository
}

func NewOrderDomainService(
	orderDomainRepo ports.OrderDomainRepository,
	orderRepo ports.OrderRepository,
	orderNamespaceRepo ports.OrderNamespaceRepository,
) ports.OrderDomainService {
	return &OrderDomainService{
		orderDomainRepo:    orderDomainRepo,
		orderRepo:          orderRepo,
		orderNamespaceRepo: orderNamespaceRepo,
	}
}

func (s *OrderDomainService) Create(name string, orderID uint64) (*domain.OrderDomain, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}
	if orderID == 0 {
		return nil, errors.New("order ID is required")
	}

	// Verify order exists
	_, err := s.orderRepo.FindByID(orderID)
	if err != nil {
		return nil, errors.New("order not found")
	}

	// Create order domain with is_available set to false as per requirements
	orderDomain := &domain.OrderDomain{
		Name:        name,
		IsAvailable: false, // Always set to false on creation
		OrderID:     orderID,
	}

	err = s.orderDomainRepo.Insert(orderDomain)
	if err != nil {
		return nil, err
	}

	return orderDomain, nil
}

func (s *OrderDomainService) GetAll(filter *ports.OrderDomainFilter) ([]*domain.OrderDomain, error) {
	return s.orderDomainRepo.FindAll(filter)
}

func (s *OrderDomainService) GetByID(id uint64) (*domain.OrderDomain, error) {
	if id == 0 {
		return nil, errors.New("id is required")
	}

	orderDomain, err := s.orderDomainRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("order domain not found")
	}

	return orderDomain, nil
}

func (s *OrderDomainService) GetByNamespaceDomain(namespaceID uint64, name string) ([]*domain.OrderDomain, error) {
	// Input validation
	if namespaceID == 0 {
		return nil, errors.New("namespace ID is required")
	}
	if name == "" {
		return nil, errors.New("name is required")
	}

	// First, find all order namespaces for the given namespace ID
	orderNamespaceFilter := &ports.OrderNamespaceFilter{
		NamespaceID: &namespaceID,
	}

	orderNamespaces, err := s.orderNamespaceRepo.FindAll(orderNamespaceFilter)
	if err != nil {
		return nil, err
	}

	// If no order namespaces found, return empty slice
	if len(orderNamespaces) == 0 {
		return []*domain.OrderDomain{}, nil
	}

	// Extract order IDs from the order namespaces
	var orderIDs []uint64
	for _, orderNamespace := range orderNamespaces {
		orderIDs = append(orderIDs, orderNamespace.OrderID)
	}

	// Find order domains that match the name and belong to any of the orders
	var result []*domain.OrderDomain
	for _, orderID := range orderIDs {
		orderDomainFilter := &ports.OrderDomainFilter{
			Name:    &name,
			OrderID: &orderID,
		}

		orderDomains, err := s.orderDomainRepo.FindAll(orderDomainFilter)
		if err != nil {
			return nil, err
		}

		result = append(result, orderDomains...)
	}

	return result, nil
}

func (s *OrderDomainService) UpdateAvailability(id uint64, isAvailable bool) (*domain.OrderDomain, error) {
	if id == 0 {
		return nil, errors.New("id is required")
	}

	// Find existing order domain
	orderDomain, err := s.orderDomainRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("order domain not found")
	}

	// Update availability status
	orderDomain.IsAvailable = isAvailable

	err = s.orderDomainRepo.Update(orderDomain)
	if err != nil {
		return nil, err
	}

	return orderDomain, nil
}

func (s *OrderDomainService) Delete(id uint64) error {
	if id == 0 {
		return errors.New("id is required")
	}

	// Verify order domain exists
	_, err := s.orderDomainRepo.FindByID(id)
	if err != nil {
		return errors.New("order domain not found")
	}

	return s.orderDomainRepo.Delete(id)
}
