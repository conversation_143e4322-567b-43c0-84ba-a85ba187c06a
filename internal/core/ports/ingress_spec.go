package ports

import "ops-api/internal/core/domain"

// IngressSpecFilter represents filters for querying ingress specs
type IngressSpecFilter struct {
	Host      *string
	Path      *string
	ServiceID *uint64
	IngressID *uint64
}

type IngressSpecRepository interface {
	Insert(ingressSpec *domain.IngressSpec) error
	FindAll(filter *IngressSpecFilter) ([]*domain.IngressSpec, error)
	FindByID(id uint64) (*domain.IngressSpec, error)
	FindByNamespaceID(namespaceID uint64) ([]*domain.IngressSpec, error)
	Update(ingressSpec *domain.IngressSpec) error
	Delete(id uint64) error
}

type IngressSpecService interface {
	Create(host, path string, port, serviceID, ingressID uint64) (*domain.IngressSpec, error)
	GetAll(filter *IngressSpecFilter) ([]*domain.IngressSpec, error)
	GetByID(id uint64) (*domain.IngressSpec, error)
	Update(id uint64, host, path string, port, serviceID, ingressID uint64) (*domain.IngressSpec, error)
	UpdateHostsByNamespace(namespaceID uint64, newDomain string) (int, error)
	Delete(id uint64) error
}
