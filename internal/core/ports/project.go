package ports

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/dto"
)

// ProjectFilter represents filters for querying projects
type ProjectFilter struct {
	Name        *string               `json:"name,omitempty"`
	Slug        *string               `json:"slug,omitempty"`
	Type        *domain.NamespaceType `json:"type,omitempty"`
	IsActive    *bool                 `json:"is_active,omitempty"`
	ClusterID   *uint64               `json:"cluster_id,omitempty"`
	WorkspaceID *uint64               `json:"workspace_id,omitempty"`
}

type ProjectService interface {
	GetAllProjects(filter *ProjectFilter) ([]*dto.ProjectListItemResponse, error)
	GetByID(id uint64) (*dto.ProjectDetailResponse, error)
	CreateProject(req *dto.CreateProjectRequest) (*dto.CreateProjectResponse, error)
	CreateProjectWithOrder(orderID uint64) (*dto.CreateProjectResponse, error)
	CreateProjectWithTemplate(templateID uint64, name string) (*dto.CreateProjectResponse, error)
	UpdateProject(id uint64, req *dto.UpdateProjectRequest) (*dto.UpdateProjectResponse, error)
}
