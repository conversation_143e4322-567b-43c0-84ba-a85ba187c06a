package ports

import "ops-api/internal/core/domain"

// DeploymentFilter represents filters for querying deployments
type DeploymentFilter struct {
	Name        *string
	NamespaceID *uint64
}

type DeploymentRepository interface {
	Insert(deployment *domain.Deployment) error
	FindAll(filter *DeploymentFilter) ([]*domain.Deployment, error)
	FindByID(id uint64) (*domain.Deployment, error)
	Update(deployment *domain.Deployment) error
	Delete(id uint64) error
}

type DeploymentService interface {
	Create(name, image string, containerPort, replicas, namespaceID uint64) (*domain.Deployment, error)
	GetAll(filter *DeploymentFilter) ([]*domain.Deployment, error)
	GetByID(id uint64) (*domain.Deployment, error)
	Update(id uint64, name, image string, containerPort, replicas, namespaceID, statusID uint64) (*domain.Deployment, error)
	Delete(id uint64) error
}
