terraform {
  required_providers {
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 4.0"
    }
  }
}

provider "cloudflare" {
  api_token = var.cloudflare_api_token
}

resource "cloudflare_record" "record" {
  count = length(var.sub_domain_list)
  zone_id = var.cloudflare_zone_id
  name    = var.sub_domain_list[count.index]
  value   = var.ip_address
  type    = "A"
  proxied = true
}