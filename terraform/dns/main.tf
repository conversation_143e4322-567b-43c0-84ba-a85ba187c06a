terraform {
  backend "pg" {
    conn_str             = ""
    schema_name          = ""
    skip_schema_creation = false
  }
  required_providers {
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 4.0"
    }
  }
}

provider "cloudflare" {
  api_token = var.cloudflare_api_token
}

resource "cloudflare_record" "record" {
  count = length(jsondecode(data.http.domains.body)["data"])
  zone_id = var.cloudflare_zone_id
  name    = jsondecode(data.http.domains.body)["data"][count.index].name
  value   = var.ip_address
  type    = "A"
  proxied = true
}

